name: Create Release

on:
  workflow_dispatch:
    inputs:
      module_version:
        description: 'Terraform module release version (e.g., v1.2.3)'
        required: true
        type: string
      services_version:
        description: 'Lock onto Braintrust services version (e.g., v1.2.3). Optional.'
        required: false
        type: string

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write

    steps:
      - name: Generate GitHub App token
        uses: actions/create-github-app-token@v1
        id: bot-token
        with:
          app-id: ${{ secrets.GH_BOT_APP_ID }}
          private-key: ${{ secrets.GH_BOT_APP_PRIVATE_KEY }}

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ steps.bot-token.outputs.token }}
          ref: main

      - name: Install the latest version of uv
        uses: astral-sh/setup-uv@v6

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::872608195481:role/github_ecr_full_access
          aws-region: us-east-1

      - name: Update versions of Braintrust Services
        if: inputs.services_version != ''
        run: |
          git config --global user.name "Braintrust Bot"
          git config --global user.email "<EMAIL>"
          ./lock_versions ${{ inputs.services_version }}
          git add .
          git commit -m "Update Braintrust Services versions to ${{ inputs.services_version }}"
          git push origin main

      - name: Create GitHub Release
        run: |
          if [ "${{ inputs.services_version }}" != "" ]; then
            cat > release_notes.md << EOF

          ## Braintrust Services
          * Updated Braintrust Services to \`${{ inputs.services_version }}\`
          EOF
            gh release create ${{ inputs.module_version }} \
              --draft \
              --generate-notes \
              --notes-file release_notes.md \
              --title "${{ inputs.module_version }}"
          else
            gh release create ${{ inputs.module_version }} \
              --draft \
              --generate-notes \
              --title "${{ inputs.module_version }}"
          fi
        env:
          GH_TOKEN: ${{ steps.bot-token.outputs.token }}
