#!/usr/bin/env -S uv --quiet run --script
# /// script
# dependencies = [
# ]
# ///

"""
This script locks specific versions of lambdas and containers as a part of the
release process of the terraform module.

Versions are written to a VERSIONS.json file in their respective module directories.
The terraform module will look at these files to determine the versions to deploy.

Usage:
    ./lock_versions VERSION_TAG
"""

import json
import sys
import os


def main():
    if len(sys.argv) < 2:
        print("ERROR: Version tag is required.")
        print("Usage: ./lock_versions <version_tag>")
        print("Example: ./lock_versions v1.0.0")
        sys.exit(1)

    version_tag = sys.argv[1]

    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Write lambda version tag to services/VERSIONS.json
    print("Writing lambda version tag to modules/services/VERSIONS.json...")
    versions_path = f"{script_dir}/modules/services/VERSIONS.json"
    lambda_versions = {"lambda_version_tag": version_tag}
    with open(versions_path, "w") as f:
        json.dump(lambda_versions, f, indent=4)

    # Write brainstore version to brainstore/VERSIONS.json
    print("Writing brainstore version to modules/brainstore/VERSIONS.json...")
    brainstore_versions_path = f"{script_dir}/modules/brainstore/VERSIONS.json"
    os.makedirs(os.path.dirname(brainstore_versions_path), exist_ok=True)
    with open(brainstore_versions_path, "w") as f:
        json.dump({"brainstore": version_tag}, f, indent=4)


if __name__ == "__main__":
    main()
